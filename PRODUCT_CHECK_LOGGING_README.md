# Product Check API Logging Enhancement

## Overview

The `/product_check` API endpoint has been enhanced with comprehensive usage metrics logging. This implementation captures detailed information about each API call, including token usage, performance metrics, and request/response data.

## ✅ Recent Fixes (2025-07-31)

### Issue 1: Token Usage Extraction Fixed
**Problem**: Token usage was showing all zero values due to incorrect extraction logic.
**Solution**: Fixed the token extraction to properly read from `response_metadata['token_usage']` instead of `usage_metadata`.
**Result**: Now correctly captures input tokens (~145), output tokens (~1), and total tokens (~146) from OpenRouter.ai API responses.

### Issue 2: Consolidated Session Logging Implemented
**Problem**: Each API call created a separate JSON file, leading to file proliferation.
**Solution**: Implemented session-based logging where all requests from a single process are stored in one file.
**Result**: Single file per process with structure:
```json
{
  "process_id": 908392,
  "session_start": "2025-07-31T11:26:21.965807+00:00",
  "requests": [
    { /* request 1 */ },
    { /* request 2 */ },
    { /* etc... */ }
  ]
}
```

## Features

### 1. Token Count Extraction
- Extracts input, output, and total token counts from OpenRouter.ai API responses
- Handles multiple response formats from LangChain ChatOpenAI
- Supports both `usage_metadata` and `response_metadata` formats

### 2. Comprehensive Logging
The logging captures the following information:

#### Request Data
- `title_1688`: The 1688 product title
- `title_ozon`: The Ozon product title

#### Response Data
- `is_match`: Boolean result of the product comparison

#### LLM Details
- `model_name`: The OpenRouter model used (e.g., "openai/gpt-4.1-mini")
- `raw_response_content`: The raw text response from the LLM
- `response_id`: Unique response identifier from OpenRouter

#### Token Usage
- `input_tokens`: Number of tokens in the input prompt
- `output_tokens`: Number of tokens in the model's response
- `total_tokens`: Sum of input and output tokens

#### Performance Metrics
- `request_duration_seconds`: Total request processing time
- `start_time`: Unix timestamp when request started
- `end_time`: Unix timestamp when request completed

#### Error Information
- `error`: Error message if the request failed (null for successful requests)

#### Metadata
- `timestamp`: ISO format timestamp with timezone
- `api_endpoint`: Always "/product_check"
- `process_id`: Process ID for debugging
- `filename`: Name of the log file

### 3. File Naming Convention
Session log files are saved with the following naming pattern:
```
product_check_session_ProcessID.json
```

Example: `product_check_session_908392.json`

**Legacy Format**: Old individual files used `product_check_YYYYMMDD_HHMMSS-ProcessID.json` but this has been replaced with the session format.

### 4. Storage Location
All log files are stored in the `product_check_history/` directory, which is automatically created if it doesn't exist.

## Implementation Details

### Code Structure
The logging functionality is implemented through:

1. **`log_product_check_usage()` function**: Core logging function that handles data extraction and file writing
2. **Enhanced `/product_check` endpoint**: Modified to capture timing and call the logging function
3. **Error handling**: Ensures logging occurs even if the API request fails

### Token Usage Extraction
The implementation correctly extracts token usage from OpenRouter.ai responses:

```python
# Primary: OpenRouter.ai puts token usage in response_metadata
if hasattr(llm_response, 'response_metadata') and llm_response.response_metadata:
    response_meta = llm_response.response_metadata
    if 'token_usage' in response_meta:
        token_usage = response_meta['token_usage']
        input_tokens = token_usage.get('prompt_tokens', 0)
        output_tokens = token_usage.get('completion_tokens', 0)
        total_tokens = token_usage.get('total_tokens', 0)

# Fallback: Check usage_metadata (usually empty for OpenRouter)
elif hasattr(llm_response, 'usage_metadata') and llm_response.usage_metadata:
    usage = llm_response.usage_metadata
    input_tokens = getattr(usage, 'input_tokens', 0)
    output_tokens = getattr(usage, 'output_tokens', 0)
    total_tokens = getattr(usage, 'total_tokens', 0)
```

**Note**: The order is important - OpenRouter.ai puts actual token counts in `response_metadata['token_usage']`, while `usage_metadata` is typically empty.

### Performance Tracking
Request timing is captured using Python's `time.time()`:

```python
start_time = time.time()
# ... API processing ...
end_time = time.time()
duration = end_time - start_time
```

## Usage Examples

### Example Log File Structure
See `example_product_check_log.json` for a complete example of the log file structure.

### Testing the Implementation
Use the provided test script:

```bash
python test_product_check_logging.py
```

This script will:
1. Make test API calls to `/product_check`
2. Verify log files are created
3. Analyze the log structure
4. Display key metrics

### API Request Example
```bash
curl -X POST "http://localhost:8000/product_check" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "title_1688": "适用华为Pura80ultra磁吸手机壳",
    "title_ozon": "Чехол для телефона Huawei Pura 70 Ultra"
  }'
```

## Benefits

1. **Cost Tracking**: Monitor token usage and associated costs
2. **Performance Analysis**: Track request latency and identify bottlenecks
3. **Usage Analytics**: Understand API usage patterns
4. **Debugging**: Detailed logs help troubleshoot issues
5. **Compliance**: Maintain audit trails for API usage

## File Management

### Automatic Cleanup
Consider implementing log rotation or cleanup policies for production use:

```python
# Example cleanup script (not implemented)
def cleanup_old_logs(days_to_keep=30):
    cutoff_date = datetime.now() - timedelta(days=days_to_keep)
    # Remove files older than cutoff_date
```

### Log Analysis
The JSON format makes it easy to analyze logs programmatically:

```python
import json
import pandas as pd

# Load and analyze logs
logs = []
for log_file in Path("product_check_history").glob("*.json"):
    with open(log_file) as f:
        logs.append(json.load(f))

df = pd.DataFrame(logs)
print(f"Average tokens per request: {df['token_usage'].apply(lambda x: x['total_tokens']).mean()}")
print(f"Average response time: {df['performance'].apply(lambda x: x['request_duration_seconds']).mean()}")
```

## Dependencies

The logging functionality requires the following imports (already included in app.py):
- `time`: For performance timing
- `json`: For log file serialization
- `pathlib.Path`: For file system operations
- `datetime`: For timestamp generation

## Error Handling

The logging implementation includes robust error handling:
- Logging failures don't affect API functionality
- Errors in logging are logged to the main application logger
- Missing token usage data defaults to 0 values
- Failed log writes are gracefully handled

## Future Enhancements

Potential improvements for the logging system:
1. **Batch logging**: Collect multiple entries before writing
2. **Database storage**: Store logs in a database for better querying
3. **Real-time monitoring**: Stream logs to monitoring systems
4. **Cost calculation**: Add actual cost calculations based on token usage
5. **Aggregation**: Daily/weekly summary reports

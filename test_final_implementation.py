#!/usr/bin/env python3
"""
Final test of the fixed product_check logging implementation.
Tests both token extraction and consolidated session logging.
"""

import requests
import json
import os
import time
from pathlib import Path

# Configuration
API_BASE_URL = "http://localhost:8000"
API_KEY = os.getenv("SEERFAR_API_KEY", "your-api-key-here")

def test_final_implementation():
    """Test the complete fixed implementation."""
    
    print("🎯 Final Implementation Test")
    print("=" * 50)
    print("Testing both fixes:")
    print("1. ✅ Token extraction from OpenRouter.ai response_metadata")
    print("2. ✅ Consolidated session logging")
    print()
    
    # Get current process ID to track our session file
    current_pid = os.getpid()
    session_file = Path(f"product_check_history/product_check_session_{current_pid}.json")
    
    # Remove existing session file for clean test
    if session_file.exists():
        session_file.unlink()
        print(f"🗑️  Removed existing session file for clean test")
    
    # Test cases
    test_cases = [
        {
            "title_1688": "Apple iPhone 15 Pro Max 256GB 钛金属",
            "title_ozon": "Apple iPhone 15 Pro Max 256 ГБ, титан",
            "expected_match": True
        },
        {
            "title_1688": "儿童玩具汽车模型合金回力小汽车",
            "title_ozon": "Tecno Смартфон SPARK 30C 8/256 ГБ, черный",
            "expected_match": False
        },
        {
            "title_1688": "华为无线蓝牙耳机TWS降噪运动耳机",
            "title_ozon": "Apple Наушники беспроводные AirPods Pro 2",
            "expected_match": True
        }
    ]
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": API_KEY
    }
    
    print(f"📤 Making {len(test_cases)} API requests to test session logging...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i} ---")
        print(f"Title 1688: {test_case['title_1688'][:40]}...")
        print(f"Title Ozon: {test_case['title_ozon'][:40]}...")
        print(f"Expected: {test_case['expected_match']}")
        
        try:
            # Make API request
            response = requests.post(
                f"{API_BASE_URL}/product_check",
                headers=headers,
                json={
                    "title_1688": test_case['title_1688'],
                    "title_ozon": test_case['title_ozon']
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                actual_match = result.get('is_match', False)
                print(f"✅ API Response: is_match={actual_match}")
                
                # Check if prediction matches expectation
                if actual_match == test_case['expected_match']:
                    print(f"🎯 Prediction matches expectation!")
                else:
                    print(f"⚠️  Prediction differs from expectation")
                    
            else:
                print(f"❌ API Error: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Request failed: {str(e)}")
        
        # Small delay between requests
        time.sleep(1)
    
    # Analyze the session file
    print("\n--- Session File Analysis ---")
    
    if session_file.exists():
        print(f"✅ Session file created: {session_file.name}")
        
        try:
            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            print(f"📊 Session Summary:")
            print(f"   Process ID: {session_data.get('process_id')}")
            print(f"   Session Start: {session_data.get('session_start')}")
            print(f"   Total Requests: {len(session_data.get('requests', []))}")
            
            # Analyze each request
            requests_list = session_data.get('requests', [])
            total_tokens = 0
            total_duration = 0
            
            print(f"\n📋 Request Details:")
            for idx, req in enumerate(requests_list, 1):
                token_usage = req.get('token_usage', {})
                performance = req.get('performance', {})
                response_data = req.get('response_data', {})
                
                input_tokens = token_usage.get('input_tokens', 0)
                output_tokens = token_usage.get('output_tokens', 0)
                req_total_tokens = token_usage.get('total_tokens', 0)
                duration = performance.get('request_duration_seconds', 0)
                is_match = response_data.get('is_match')
                
                total_tokens += req_total_tokens
                total_duration += duration
                
                print(f"   Request #{idx}:")
                print(f"      Tokens: {input_tokens} input + {output_tokens} output = {req_total_tokens} total")
                print(f"      Duration: {duration:.3f}s")
                print(f"      Result: is_match={is_match}")
                
                # Verify token extraction is working
                if req_total_tokens > 0:
                    print(f"      ✅ Token extraction working correctly")
                else:
                    print(f"      ❌ Token extraction failed")
            
            print(f"\n📈 Session Totals:")
            print(f"   Total tokens consumed: {total_tokens}")
            print(f"   Average tokens per request: {total_tokens/len(requests_list):.1f}")
            print(f"   Total processing time: {total_duration:.3f}s")
            print(f"   Average response time: {total_duration/len(requests_list):.3f}s")
            
            # Verify both fixes
            print(f"\n🔍 Fix Verification:")
            
            # Fix 1: Token extraction
            if all(req.get('token_usage', {}).get('total_tokens', 0) > 0 for req in requests_list):
                print(f"   ✅ Fix 1: Token extraction working for all requests")
            else:
                print(f"   ❌ Fix 1: Token extraction still failing")
            
            # Fix 2: Consolidated logging
            if len(requests_list) == len(test_cases):
                print(f"   ✅ Fix 2: All requests logged in single session file")
            else:
                print(f"   ❌ Fix 2: Request count mismatch")
            
            # Estimate cost
            estimated_cost = total_tokens * 0.00001  # Rough estimate
            print(f"\n💰 Estimated cost: ${estimated_cost:.6f}")
            
        except Exception as e:
            print(f"❌ Error reading session file: {str(e)}")
    else:
        print(f"❌ Session file not created: {session_file}")

def cleanup_test_files():
    """Clean up test session files."""
    print("\n🧹 Cleanup")
    current_pid = os.getpid()
    session_file = Path(f"product_check_history/product_check_session_{current_pid}.json")
    
    if session_file.exists():
        print(f"🗑️  Test session file kept for analysis: {session_file.name}")
        print(f"   You can delete it manually if needed")
    else:
        print(f"ℹ️  No test session file to clean up")

if __name__ == "__main__":
    print("🚀 Final Product Check Implementation Test")
    print("=" * 60)
    
    try:
        test_final_implementation()
    finally:
        cleanup_test_files()
    
    print("\n✅ Final test completed!")
    print("\n📝 Summary:")
    print("   - Token extraction now works correctly (reading from response_metadata)")
    print("   - Session logging consolidates all requests per process")
    print("   - Both issues have been successfully resolved")

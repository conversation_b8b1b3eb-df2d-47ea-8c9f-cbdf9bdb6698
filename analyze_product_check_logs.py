#!/usr/bin/env python3
"""
Utility script to analyze product_check API usage logs.
Provides insights into token usage, performance metrics, and usage patterns.
"""

import json
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Any

def load_log_files(log_dir: str = "product_check_history") -> List[Dict[Any, Any]]:
    """Load all log files from the product_check_history directory."""
    log_path = Path(log_dir)
    if not log_path.exists():
        print(f"❌ Log directory {log_dir} not found")
        return []

    logs = []

    # Load both old individual files and new session files
    individual_files = list(log_path.glob("product_check_2*.json"))  # Old format
    session_files = list(log_path.glob("product_check_session_*.json"))  # New format

    print(f"📁 Found {len(individual_files)} individual log files and {len(session_files)} session files")

    # Load individual files (old format)
    for log_file in individual_files:
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                log_data = json.load(f)
                logs.append(log_data)
        except Exception as e:
            print(f"❌ Error reading {log_file}: {str(e)}")

    # Load session files (new format)
    for session_file in session_files:
        try:
            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
                # Extract individual requests from session
                requests = session_data.get('requests', [])
                for request in requests:
                    # Add session metadata to each request
                    request['session_metadata'] = {
                        'process_id': session_data.get('process_id'),
                        'session_start': session_data.get('session_start'),
                        'session_file': session_file.name
                    }
                    logs.append(request)
        except Exception as e:
            print(f"❌ Error reading {session_file}: {str(e)}")

    return logs

def analyze_token_usage(logs: List[Dict[Any, Any]]) -> None:
    """Analyze token usage patterns."""
    print("\n📊 Token Usage Analysis")
    print("=" * 50)
    
    if not logs:
        print("❌ No logs to analyze")
        return
    
    # Extract token data
    token_data = []
    for log in logs:
        token_usage = log.get('token_usage', {})
        token_data.append({
            'timestamp': log.get('timestamp'),
            'input_tokens': token_usage.get('input_tokens', 0),
            'output_tokens': token_usage.get('output_tokens', 0),
            'total_tokens': token_usage.get('total_tokens', 0),
            'model': log.get('llm_details', {}).get('model_name', 'unknown')
        })
    
    # Calculate statistics manually
    total_tokens = [d['total_tokens'] for d in token_data]
    input_tokens = [d['input_tokens'] for d in token_data]
    output_tokens = [d['output_tokens'] for d in token_data]

    # Summary statistics
    print(f"📈 Total API calls: {len(token_data)}")
    if token_data:
        print(f"📈 Average input tokens: {sum(input_tokens)/len(input_tokens):.1f}")
        print(f"📈 Average output tokens: {sum(output_tokens)/len(output_tokens):.1f}")
        print(f"📈 Average total tokens: {sum(total_tokens)/len(total_tokens):.1f}")
        print(f"📈 Total tokens consumed: {sum(total_tokens)}")

        # Token distribution
        sorted_tokens = sorted(total_tokens)
        print(f"\n🔍 Token Distribution:")
        print(f"   Min total tokens: {min(sorted_tokens)}")
        print(f"   Max total tokens: {max(sorted_tokens)}")
        print(f"   Median total tokens: {sorted_tokens[len(sorted_tokens)//2]}")
        print(f"   95th percentile: {sorted_tokens[int(len(sorted_tokens)*0.95)]:.1f}")

def analyze_performance(logs: List[Dict[Any, Any]]) -> None:
    """Analyze API performance metrics."""
    print("\n⚡ Performance Analysis")
    print("=" * 50)
    
    if not logs:
        print("❌ No logs to analyze")
        return
    
    # Extract performance data
    perf_data = []
    for log in logs:
        performance = log.get('performance', {})
        perf_data.append({
            'timestamp': log.get('timestamp'),
            'duration': performance.get('request_duration_seconds', 0),
            'model': log.get('llm_details', {}).get('model_name', 'unknown'),
            'has_error': log.get('error') is not None
        })
    
    # Calculate statistics manually
    durations = [d['duration'] for d in perf_data]
    errors = [d['has_error'] for d in perf_data]

    if perf_data:
        # Performance statistics
        sorted_durations = sorted(durations)
        print(f"⏱️  Average response time: {sum(durations)/len(durations):.3f}s")
        print(f"⏱️  Fastest response: {min(durations):.3f}s")
        print(f"⏱️  Slowest response: {max(durations):.3f}s")
        print(f"⏱️  Median response time: {sorted_durations[len(sorted_durations)//2]:.3f}s")
        print(f"⏱️  95th percentile: {sorted_durations[int(len(sorted_durations)*0.95)]:.3f}s")

        # Error rate
        error_rate = sum(errors) / len(errors) * 100
        print(f"❌ Error rate: {error_rate:.1f}%")

def analyze_usage_patterns(logs: List[Dict[Any, Any]]) -> None:
    """Analyze usage patterns over time."""
    print("\n📅 Usage Patterns")
    print("=" * 50)
    
    if not logs:
        print("❌ No logs to analyze")
        return
    
    # Extract timestamp data
    timestamps = []
    for log in logs:
        try:
            timestamp = datetime.fromisoformat(log.get('timestamp', '').replace('Z', '+00:00'))
            timestamps.append(timestamp)
        except:
            continue
    
    if not timestamps:
        print("❌ No valid timestamps found")
        return
    
    # Time range
    start_time = min(timestamps)
    end_time = max(timestamps)
    duration = end_time - start_time
    
    print(f"📅 Time range: {start_time.strftime('%Y-%m-%d %H:%M')} to {end_time.strftime('%Y-%m-%d %H:%M')}")
    print(f"📅 Total duration: {duration}")
    if duration.total_seconds() > 0:
        print(f"📅 Average requests per hour: {len(timestamps) / max(duration.total_seconds() / 3600, 1):.1f}")
    else:
        print(f"📅 All requests in same time period")

def analyze_model_usage(logs: List[Dict[Any, Any]]) -> None:
    """Analyze which models are being used."""
    print("\n🤖 Model Usage Analysis")
    print("=" * 50)
    
    if not logs:
        print("❌ No logs to analyze")
        return
    
    # Count model usage
    model_counts = {}
    total_tokens_by_model = {}
    
    for log in logs:
        model = log.get('llm_details', {}).get('model_name', 'unknown')
        tokens = log.get('token_usage', {}).get('total_tokens', 0)
        
        model_counts[model] = model_counts.get(model, 0) + 1
        total_tokens_by_model[model] = total_tokens_by_model.get(model, 0) + tokens
    
    print("📊 Model usage:")
    for model, count in sorted(model_counts.items(), key=lambda x: x[1], reverse=True):
        tokens = total_tokens_by_model.get(model, 0)
        print(f"   {model}: {count} calls, {tokens} total tokens")

def analyze_match_results(logs: List[Dict[Any, Any]]) -> None:
    """Analyze product matching results."""
    print("\n🎯 Match Results Analysis")
    print("=" * 50)
    
    if not logs:
        print("❌ No logs to analyze")
        return
    
    # Count match results
    match_counts = {'true': 0, 'false': 0, 'error': 0}
    
    for log in logs:
        if log.get('error'):
            match_counts['error'] += 1
        else:
            is_match = log.get('response_data', {}).get('is_match')
            if is_match is True:
                match_counts['true'] += 1
            elif is_match is False:
                match_counts['false'] += 1
    
    total = sum(match_counts.values())
    if total > 0:
        print(f"✅ Matches: {match_counts['true']} ({match_counts['true']/total*100:.1f}%)")
        print(f"❌ Non-matches: {match_counts['false']} ({match_counts['false']/total*100:.1f}%)")
        print(f"💥 Errors: {match_counts['error']} ({match_counts['error']/total*100:.1f}%)")

def generate_summary_report(logs: List[Dict[Any, Any]]) -> None:
    """Generate a comprehensive summary report."""
    print("\n📋 Summary Report")
    print("=" * 50)
    
    if not logs:
        print("❌ No logs to analyze")
        return
    
    # Basic stats
    total_calls = len(logs)
    total_tokens = sum(log.get('token_usage', {}).get('total_tokens', 0) for log in logs)
    avg_duration = sum(log.get('performance', {}).get('request_duration_seconds', 0) for log in logs) / total_calls
    
    print(f"📊 Total API calls: {total_calls}")
    print(f"🔢 Total tokens consumed: {total_tokens:,}")
    print(f"⏱️  Average response time: {avg_duration:.3f}s")
    
    # Estimated cost (rough estimate based on OpenAI pricing)
    # Note: Actual costs may vary based on OpenRouter pricing
    estimated_cost = total_tokens * 0.00001  # Very rough estimate
    print(f"💰 Estimated cost: ${estimated_cost:.4f} (rough estimate)")

def main():
    """Main analysis function."""
    print("🔍 Product Check Log Analysis")
    print("=" * 50)
    
    # Load logs
    logs = load_log_files()
    
    if not logs:
        print("❌ No logs found to analyze")
        return
    
    # Run all analyses
    analyze_token_usage(logs)
    analyze_performance(logs)
    analyze_usage_patterns(logs)
    analyze_model_usage(logs)
    analyze_match_results(logs)
    generate_summary_report(logs)
    
    print("\n✅ Analysis complete!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Direct test of the fixed token extraction logic.
"""

import os
import sys
import json
from pathlib import Path

# Add the current directory to Python path to import from app.py
sys.path.insert(0, str(Path(__file__).parent))

from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from pydantic import SecretStr
from app import log_product_check_usage, ProductCheckRequest, ProductCheckResponse
import time

load_dotenv()

def test_token_extraction_direct():
    """Test token extraction directly using the fixed logging function."""
    
    print("🧪 Direct Token Extraction Test")
    print("=" * 40)
    
    # Initialize LLM exactly like in the product_check endpoint
    llm = ChatOpenAI(
        base_url='https://openrouter.ai/api/v1',
        model='openai/gpt-4.1-mini',
        temperature=0,
        api_key=SecretStr(str(os.getenv("OPENROUTER_API_KEY"))),
    )
    
    # Test prompt
    test_prompt = """
You are an expert product matcher. Compare the following two product titles and determine if they refer to the same category. 

Title 1: "适用华为Pura80ultra磁吸手机壳Mate70Pro高透不发黄Nova14超薄壳"
Title 2: "Чехол для телефона Huawei Pura 70 Ultra прозрачный силиконовый"

Note: The two titles may be in different languages. Use translation or cross-lingual understanding if needed to make your judgment.

Return ONLY a single word: True if they refer to the same category, or False if they do not. Do not return any explanation or extra text.
"""
    
    try:
        print("📤 Making LLM request...")
        start_time = time.time()
        response = llm.invoke(test_prompt)
        end_time = time.time()
        
        print(f"✅ Response received: {response.content}")
        
        # Create mock request/response objects
        mock_request = ProductCheckRequest(
            title_1688="适用华为Pura80ultra磁吸手机壳Mate70Pro高透不发黄Nova14超薄壳",
            title_ozon="Чехол для телефона Huawei Pura 70 Ultra прозрачный силиконовый"
        )
        
        is_match = str(response.content).strip().lower() == 'true'
        mock_response = ProductCheckResponse(is_match=is_match)
        
        print(f"📝 Parsed result: is_match={is_match}")
        
        # Test the logging function
        print("📊 Testing logging function...")
        log_product_check_usage(
            request_data=mock_request,
            response_data=mock_response,
            llm_response=response,
            model_name='openai/gpt-4.1-mini',
            start_time=start_time,
            end_time=end_time,
            error=None
        )
        
        print("✅ Logging function completed successfully")
        
        # Check the session file
        process_id = os.getpid()
        session_file = Path(f"product_check_history/product_check_session_{process_id}.json")
        
        if session_file.exists():
            print(f"📄 Reading session file: {session_file}")
            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            requests_list = session_data.get('requests', [])
            if requests_list:
                latest_request = requests_list[-1]
                token_usage = latest_request.get('token_usage', {})
                
                print(f"🔍 Token Usage Results:")
                print(f"   Input tokens: {token_usage.get('input_tokens', 0)}")
                print(f"   Output tokens: {token_usage.get('output_tokens', 0)}")
                print(f"   Total tokens: {token_usage.get('total_tokens', 0)}")
                
                if token_usage.get('total_tokens', 0) > 0:
                    print("✅ Token extraction is working correctly!")
                else:
                    print("❌ Token extraction is still failing")
                    
                    # Debug: show the raw response structure
                    print("\n🔍 Debug: Raw response attributes:")
                    for attr in ['usage_metadata', 'response_metadata']:
                        if hasattr(response, attr):
                            value = getattr(response, attr)
                            print(f"   {attr}: {value}")
            else:
                print("❌ No requests found in session file")
        else:
            print("❌ Session file not created")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_token_extraction_direct()

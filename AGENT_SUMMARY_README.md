# Agent History Summarization

This document describes the asynchronous agent history summarization functionality that automatically generates comprehensive summaries after each agent execution.

## Overview

The system automatically creates summary files in the `agent_summary/` directory whenever a new agent history JSON file is saved. This happens asynchronously to avoid blocking the main application flow.

## File Structure

### Input Files
- **Location**: `agent_history/`
- **Format**: `agent_history_{timestamp}_{process_id}.json`
- **Content**: Complete agent execution history with steps, metadata, and timing information

### Output Files
- **Location**: `agent_summary/`
- **Format**: `agent_summary_{process_id}_{timestamp}.json`
- **Content**: Comprehensive summary data extracted from the agent history

## Summary Data Structure

```json
{
  "process_id": "cfb73689-78c0-437d-8f2a-bc875db12555",
  "total_input_tokens": 126866,
  "session_start_time": "2025-08-01T02:12:53.899465+00:00",
  "session_end_time": "2025-08-01T02:17:17.055124+00:00",
  "total_duration_seconds": 263.16,
  "total_steps": 16,
  "keywords_extracted": ["автозапчасти", "search", "product_analysis"],
  "summary_timestamp": "2025-08-01T03:56:30.110489+00:00"
}
```

## Data Extraction Logic

### 1. Process ID
- Extracted from the filename or JSON content
- Used as the unique identifier for the session

### 2. Total Input Tokens
- Sums all `input_tokens` values from `metadata` across all steps
- Provides insight into the computational cost of the session

### 3. Session Timing
- **session_start_time**: Earliest `step_start_time` from all steps
- **session_end_time**: Latest `step_end_time` from all steps
- **total_duration_seconds**: Calculated difference between start and end times

### 4. Total Steps
- Counts the number of steps/actions in the agent history
- Indicates the complexity of the task execution

### 5. Keywords Extraction
- Extracts text content from `model_output -> action -> text` fields
- Filters out common stop words
- Supports both English and Russian keywords (Cyrillic characters)
- Limited to 10 unique keywords per summary

## Implementation Details

### Core Functions

#### `generate_agent_summary_async(history_file_path, process_id)`
- **Purpose**: Main asynchronous function that orchestrates the summarization process
- **Execution**: Called automatically after agent history file creation
- **Error Handling**: Comprehensive error handling that doesn't block the main application

#### `extract_summary_data(history_data, process_id)`
- **Purpose**: Extracts and calculates summary metrics from raw history data
- **Returns**: Structured summary data dictionary
- **Fallback**: Returns empty summary structure if data is malformed

#### `extract_keywords_from_text(text)`
- **Purpose**: Extracts meaningful keywords from text content
- **Features**: 
  - Supports Unicode characters (Russian/Cyrillic)
  - Filters common stop words
  - Limits output to prevent noise

### Integration Point

The summarization is triggered in the `save_agent_history_with_process_id()` function:

```python
# Save the history data to JSON file
with open(filepath, 'w', encoding='utf-8') as f:
    json.dump(history_data, f, indent=2, ensure_ascii=False)

logger.info(f"Agent history saved successfully to: {filepath}")

# Generate summary asynchronously after saving the history file
asyncio.create_task(generate_agent_summary_async(str(filepath), process_id))
```

## Error Handling

- **File Reading Errors**: Gracefully handled with logging
- **Malformed JSON**: Falls back to empty summary structure
- **Missing Data**: Uses default values and logs warnings
- **Async Execution**: Errors don't propagate to main application flow

## Performance Considerations

- **Asynchronous Execution**: Summarization runs in background without blocking
- **Memory Efficient**: Processes files one at a time
- **Keyword Limiting**: Prevents excessive memory usage from large text content
- **Error Isolation**: Failures don't affect main application functionality

## Usage Examples

### Automatic Generation
Summaries are generated automatically when agent history files are created. No manual intervention required.

### Manual Testing
For testing purposes, you can call the function directly:

```python
import asyncio
from app import generate_agent_summary_async

# Generate summary for existing history file
await generate_agent_summary_async(
    "agent_history/agent_history_20250801_021938_cfb73689-78c0-437d-8f2a-bc875db12555.json",
    "cfb73689-78c0-437d-8f2a-bc875db12555"
)
```

## Directory Structure

```
project_root/
├── agent_history/           # Input: Agent execution history files
│   └── agent_history_{timestamp}_{process_id}.json
├── agent_summary/           # Output: Generated summary files
│   └── agent_summary_{process_id}_{timestamp}.json
└── app.py                   # Main application with summarization integration
```

## Monitoring and Logging

The system provides comprehensive logging for:
- Summary generation start/completion
- File creation success
- Error conditions and recovery
- Data extraction warnings

All logs use the existing application logger with appropriate log levels.

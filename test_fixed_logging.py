#!/usr/bin/env python3
"""
Test script for the fixed product_check logging implementation.
Tests both token extraction and consolidated logging.
"""

import requests
import json
import os
import time
from pathlib import Path

# Configuration
API_BASE_URL = "http://localhost:8000"
API_KEY = os.getenv("SEERFAR_API_KEY", "your-api-key-here")

def test_fixed_logging():
    """Test the fixed product_check endpoint logging."""
    
    print("🔧 Testing Fixed Product Check Logging")
    print("=" * 50)
    
    # Test data
    test_cases = [
        {
            "title_1688": "适用华为Pura80ultra磁吸手机壳Mate70Pro高透不发黄Nova14超薄壳",
            "title_ozon": "Чехол для телефона Huawei Pura 70 Ultra прозрачный силиконовый"
        },
        {
            "title_1688": "儿童玩具汽车模型合金回力小汽车",
            "title_ozon": "Tecno Смартфон SPARK 30C 8/256 ГБ, черный"
        },
        {
            "title_1688": "Apple iPhone 15 Pro Max 256GB",
            "title_ozon": "Apple iPhone 15 Pro Max 256 ГБ, титан"
        }
    ]
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": API_KEY
    }
    
    print(f"📤 Making {len(test_cases)} API requests...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i} ---")
        print(f"Title 1688: {test_case['title_1688'][:50]}...")
        print(f"Title Ozon: {test_case['title_ozon'][:50]}...")
        
        try:
            # Make API request
            response = requests.post(
                f"{API_BASE_URL}/product_check",
                headers=headers,
                json=test_case,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ API Response: {result}")
            else:
                print(f"❌ API Error: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Request failed: {str(e)}")
        
        # Small delay between requests
        time.sleep(1)
    
    # Check session log file
    print("\n--- Checking Session Log File ---")
    history_dir = Path("product_check_history")
    
    if history_dir.exists():
        # Look for session files
        session_files = list(history_dir.glob("product_check_session_*.json"))
        print(f"Found {len(session_files)} session files:")
        
        for session_file in session_files:
            print(f"\n📄 {session_file.name}")
            try:
                with open(session_file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
                
                # Display session information
                print(f"   Process ID: {session_data.get('process_id')}")
                print(f"   Session Start: {session_data.get('session_start')}")
                print(f"   Total Requests: {len(session_data.get('requests', []))}")
                
                # Show token usage for recent requests
                requests_list = session_data.get('requests', [])
                if requests_list:
                    print(f"   Recent requests:")
                    for idx, req in enumerate(requests_list[-3:], 1):  # Show last 3
                        token_usage = req.get('token_usage', {})
                        duration = req.get('performance', {}).get('request_duration_seconds', 0)
                        is_match = req.get('response_data', {}).get('is_match')
                        
                        print(f"      #{len(requests_list)-3+idx}: {token_usage.get('total_tokens', 0)} tokens, "
                              f"{duration:.3f}s, match={is_match}")
                        
                        # Check if token extraction is working
                        if token_usage.get('total_tokens', 0) > 0:
                            print(f"         ✅ Token extraction working: "
                                  f"input={token_usage.get('input_tokens', 0)}, "
                                  f"output={token_usage.get('output_tokens', 0)}")
                        else:
                            print(f"         ❌ Token extraction still failing")
                    
            except Exception as e:
                print(f"   ❌ Error reading session file: {str(e)}")
    else:
        print("❌ product_check_history directory not found")

def analyze_session_structure():
    """Analyze the structure of session log files."""
    print("\n--- Session Structure Analysis ---")
    
    history_dir = Path("product_check_history")
    if not history_dir.exists():
        print("❌ No log directory found")
        return
    
    session_files = list(history_dir.glob("product_check_session_*.json"))
    if not session_files:
        print("❌ No session files found")
        return
    
    # Get the most recent session file
    latest_session = max(session_files, key=lambda f: f.stat().st_mtime)
    print(f"📄 Analyzing: {latest_session.name}")
    
    try:
        with open(latest_session, 'r', encoding='utf-8') as f:
            session_data = json.load(f)
        
        print("\n🔍 Session Structure:")
        print(f"   Process ID: {session_data.get('process_id')}")
        print(f"   Session Start: {session_data.get('session_start')}")
        print(f"   Number of Requests: {len(session_data.get('requests', []))}")
        
        # Show structure of first request
        requests_list = session_data.get('requests', [])
        if requests_list:
            print(f"\n📋 First Request Structure:")
            first_request = requests_list[0]
            print(json.dumps(first_request, indent=4, ensure_ascii=False))
        
        # Validate required fields
        required_fields = ['process_id', 'session_start', 'requests']
        print(f"\n✅ Session Field Validation:")
        for field in required_fields:
            if field in session_data:
                print(f"   ✅ {field}: Present")
            else:
                print(f"   ❌ {field}: Missing")
        
        # Validate request fields
        if requests_list:
            print(f"\n✅ Request Field Validation:")
            request_fields = [
                'timestamp', 'api_endpoint', 'request_data', 'response_data',
                'llm_details', 'token_usage', 'performance'
            ]
            first_request = requests_list[0]
            for field in request_fields:
                if field in first_request:
                    print(f"   ✅ {field}: Present")
                else:
                    print(f"   ❌ {field}: Missing")
                    
    except Exception as e:
        print(f"❌ Error analyzing session: {str(e)}")

if __name__ == "__main__":
    print("🚀 Fixed Product Check Logging Test")
    print("=" * 50)
    
    # Test the API endpoint
    test_fixed_logging()
    
    # Analyze session structure
    analyze_session_structure()
    
    print("\n✅ Test completed!")

#!/usr/bin/env python3
"""
Test script for product_check API endpoint logging functionality.
This script tests the enhanced logging capabilities of the /product_check endpoint.
"""

import requests
import json
import os
import time
from pathlib import Path

# Configuration
API_BASE_URL = "http://localhost:8000"
API_KEY = os.getenv("SEERFAR_API_KEY", "your-api-key-here")

def test_product_check_logging():
    """Test the product_check endpoint and verify logging functionality."""
    
    print("Testing /product_check endpoint with enhanced logging...")
    
    # Test data
    test_cases = [
        {
            "title_1688": "适用华为Pura80ultra磁吸手机壳Mate70Pro高透不发黄Nova14超薄壳",
            "title_ozon": "Чехол для телефона Huawei Pura 70 Ultra прозрачный силиконовый"
        },
        {
            "title_1688": "儿童玩具汽车模型合金回力小汽车",
            "title_ozon": "Tecno Смартфон SPARK 30C 8/256 ГБ, черный"
        }
    ]
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": API_KEY
    }
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i} ---")
        print(f"Title 1688: {test_case['title_1688']}")
        print(f"Title Ozon: {test_case['title_ozon']}")
        
        try:
            # Make API request
            response = requests.post(
                f"{API_BASE_URL}/product_check",
                headers=headers,
                json=test_case,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ API Response: {result}")
            else:
                print(f"❌ API Error: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Request failed: {str(e)}")
        
        # Small delay between requests
        time.sleep(1)
    
    # Check if log files were created
    print("\n--- Checking Log Files ---")
    history_dir = Path("product_check_history")
    
    if history_dir.exists():
        log_files = list(history_dir.glob("product_check_*.json"))
        print(f"Found {len(log_files)} log files:")
        
        for log_file in sorted(log_files)[-3:]:  # Show last 3 files
            print(f"\n📄 {log_file.name}")
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    log_data = json.load(f)
                
                # Display key information
                print(f"   Timestamp: {log_data.get('timestamp')}")
                print(f"   Model: {log_data.get('llm_details', {}).get('model_name')}")
                print(f"   Duration: {log_data.get('performance', {}).get('request_duration_seconds')}s")
                print(f"   Tokens: {log_data.get('token_usage', {}).get('total_tokens')}")
                print(f"   Result: {log_data.get('response_data', {}).get('is_match')}")
                if log_data.get('error'):
                    print(f"   Error: {log_data.get('error')}")
                    
            except Exception as e:
                print(f"   ❌ Error reading log file: {str(e)}")
    else:
        print("❌ product_check_history directory not found")

def analyze_log_structure():
    """Analyze the structure of the most recent log file."""
    print("\n--- Log Structure Analysis ---")
    
    history_dir = Path("product_check_history")
    if not history_dir.exists():
        print("❌ No log directory found")
        return
    
    log_files = list(history_dir.glob("product_check_*.json"))
    if not log_files:
        print("❌ No log files found")
        return
    
    # Get the most recent log file
    latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
    print(f"📄 Analyzing: {latest_log.name}")
    
    try:
        with open(latest_log, 'r', encoding='utf-8') as f:
            log_data = json.load(f)
        
        print("\n🔍 Log Structure:")
        print(json.dumps(log_data, indent=2, ensure_ascii=False))
        
        # Validate required fields
        required_fields = [
            'timestamp', 'api_endpoint', 'request_data', 'response_data',
            'llm_details', 'token_usage', 'performance', 'metadata'
        ]
        
        print("\n✅ Field Validation:")
        for field in required_fields:
            if field in log_data:
                print(f"   ✅ {field}: Present")
            else:
                print(f"   ❌ {field}: Missing")
                
    except Exception as e:
        print(f"❌ Error analyzing log: {str(e)}")

if __name__ == "__main__":
    print("🚀 Product Check Logging Test")
    print("=" * 50)
    
    # Test the API endpoint
    test_product_check_logging()
    
    # Analyze log structure
    analyze_log_structure()
    
    print("\n✅ Test completed!")

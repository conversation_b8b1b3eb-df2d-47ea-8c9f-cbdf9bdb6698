#!/usr/bin/env python3
"""
Debug script to test token extraction from LangChain ChatOpenAI responses.
This will help us understand the actual response structure from OpenRouter.ai.
"""

import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from pydantic import SecretStr
import json

load_dotenv()

def debug_llm_response():
    """Test LLM response and inspect its structure for token usage."""
    
    print("🔍 Debugging LangChain ChatOpenAI Response Structure")
    print("=" * 60)
    
    # Initialize LLM exactly like in the product_check endpoint
    llm = ChatOpenAI(
        base_url='https://openrouter.ai/api/v1',
        model='openai/gpt-4.1-mini',
        temperature=0,
        api_key=SecretStr(str(os.getenv("OPENROUTER_API_KEY"))),
    )
    
    # Test prompt similar to product_check
    test_prompt = """
You are an expert product matcher. Compare the following two product titles and determine if they refer to the same category. 

Title 1: "适用华为Pura80ultra磁吸手机壳Mate70Pro高透不发黄Nova14超薄壳"
Title 2: "Чехол для телефона Huawei Pura 70 Ultra прозрачный силиконовый"

Note: The two titles may be in different languages. Use translation or cross-lingual understanding if needed to make your judgment.

Return ONLY a single word: True if they refer to the same category, or False if they do not. Do not return any explanation or extra text.
"""
    
    try:
        print("📤 Making LLM request...")
        response = llm.invoke(test_prompt)
        
        print("✅ Response received!")
        print(f"📝 Content: {response.content}")
        print(f"🔧 Response type: {type(response)}")
        
        # Inspect all attributes
        print("\n🔍 Response attributes:")
        for attr in dir(response):
            if not attr.startswith('_'):
                try:
                    value = getattr(response, attr)
                    if not callable(value):
                        print(f"   {attr}: {type(value)} = {value}")
                except:
                    print(f"   {attr}: <error accessing>")
        
        # Check for usage_metadata
        print("\n📊 Checking usage_metadata:")
        if hasattr(response, 'usage_metadata'):
            usage_meta = response.usage_metadata
            print(f"   usage_metadata type: {type(usage_meta)}")
            print(f"   usage_metadata value: {usage_meta}")
            
            if usage_meta:
                print("   usage_metadata attributes:")
                for attr in dir(usage_meta):
                    if not attr.startswith('_'):
                        try:
                            value = getattr(usage_meta, attr)
                            if not callable(value):
                                print(f"      {attr}: {value}")
                        except:
                            print(f"      {attr}: <error accessing>")
        else:
            print("   ❌ No usage_metadata attribute")
        
        # Check for response_metadata
        print("\n📊 Checking response_metadata:")
        if hasattr(response, 'response_metadata'):
            resp_meta = response.response_metadata
            print(f"   response_metadata type: {type(resp_meta)}")
            print(f"   response_metadata value: {resp_meta}")
            
            if resp_meta and isinstance(resp_meta, dict):
                print("   response_metadata keys:")
                for key, value in resp_meta.items():
                    print(f"      {key}: {type(value)} = {value}")
                    
                    # Check for token_usage specifically
                    if key == 'token_usage' and isinstance(value, dict):
                        print("      token_usage details:")
                        for token_key, token_value in value.items():
                            print(f"         {token_key}: {token_value}")
        else:
            print("   ❌ No response_metadata attribute")
        
        # Try to extract tokens using current logic
        print("\n🧪 Testing current extraction logic:")
        input_tokens = 0
        output_tokens = 0
        total_tokens = 0
        
        if hasattr(response, 'usage_metadata') and response.usage_metadata:
            print("   ✅ Found usage_metadata")
            usage = response.usage_metadata
            input_tokens = getattr(usage, 'input_tokens', 0)
            output_tokens = getattr(usage, 'output_tokens', 0)
            total_tokens = getattr(usage, 'total_tokens', 0)
            print(f"   Extracted: input={input_tokens}, output={output_tokens}, total={total_tokens}")
        elif hasattr(response, 'response_metadata') and response.response_metadata:
            print("   ✅ Found response_metadata")
            response_meta = response.response_metadata
            if 'token_usage' in response_meta:
                print("   ✅ Found token_usage in response_metadata")
                token_usage = response_meta['token_usage']
                input_tokens = token_usage.get('prompt_tokens', 0)
                output_tokens = token_usage.get('completion_tokens', 0)
                total_tokens = token_usage.get('total_tokens', 0)
                print(f"   Extracted: input={input_tokens}, output={output_tokens}, total={total_tokens}")
            else:
                print("   ❌ No token_usage in response_metadata")
        else:
            print("   ❌ No usage metadata found")
        
        print(f"\n📈 Final token counts:")
        print(f"   Input tokens: {input_tokens}")
        print(f"   Output tokens: {output_tokens}")
        print(f"   Total tokens: {total_tokens}")
        
        # Save full response structure to file for analysis
        response_data = {
            'content': str(response.content),
            'type': str(type(response)),
            'attributes': {},
            'usage_metadata': None,
            'response_metadata': None
        }
        
        # Safely extract attributes
        for attr in ['usage_metadata', 'response_metadata', 'id', 'additional_kwargs']:
            if hasattr(response, attr):
                try:
                    value = getattr(response, attr)
                    if attr == 'usage_metadata' and value:
                        response_data['usage_metadata'] = {
                            'type': str(type(value)),
                            'attributes': {a: getattr(value, a) for a in dir(value) if not a.startswith('_') and not callable(getattr(value, a))}
                        }
                    elif attr == 'response_metadata':
                        response_data['response_metadata'] = value
                    else:
                        response_data['attributes'][attr] = str(value)
                except:
                    response_data['attributes'][attr] = '<error accessing>'
        
        # Save to file
        with open('debug_response_structure.json', 'w', encoding='utf-8') as f:
            json.dump(response_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 Full response structure saved to: debug_response_structure.json")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_llm_response()
